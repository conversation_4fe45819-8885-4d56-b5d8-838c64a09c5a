"""
Notebook-Style Write-Back Implementation
=======================================

This implementation follows the exact pattern from the reference notebook:
missing_logs_imputation_petrel_enhanced.ipynb

This is a simpler, cleaner approach that matches the working notebook pattern.
"""

def write_back_to_petrel(results_df, log_name_in_results, clone_from=None):
    """
    Clone an existing log (or first available log) and overwrite with imputed values.
    
    This function follows the exact pattern from the reference notebook:
    missing_logs_imputation_petrel_enhanced.ipynb
    
    Args:
        results_df: DataFrame containing the results
        log_name_in_results: Column name in results_df to write back
        clone_from: Template log to clone from (optional)
    """
    # Ensure we have the selected wells
    if 'selected_wells' not in globals():
        print('ERROR: selected_wells not found in global scope.')
        print('Please ensure you have run the well selection steps first.')
        return
    
    # Get selected_wells from global scope
    selected_wells = globals()['selected_wells']
    
    # Set default clone_from if not provided
    if clone_from is None:
        # Try to use common VP log names as template
        if 'selected_target_log' in globals():
            clone_from = globals()['selected_target_log']
        elif 'TARGET' in globals():
            clone_from = globals()['TARGET']
        else:
            clone_from = 'VP_COREL_ML'  # Default fallback
        print(f'Using template log: {clone_from}')
    
    print(f'Writing back {log_name_in_results} to {len(selected_wells)} wells...')
    success_count = 0
    
    for w in selected_wells:
        print(f'Updating {w.petrel_name}')
        well_df = results_df[results_df['WELL'] == w.petrel_name].set_index('MD')
        if well_df.empty:
            print(f'  ⚠ No data for well {w.petrel_name}, skipping')
            continue
            
        md = well_df.index.to_numpy()
        values = well_df[log_name_in_results].to_numpy()

        # Find a log to clone (or existing target)
        target_logs = [log for log in w.logs if log.petrel_name == log_name_in_results]
        if target_logs:
            log_obj = target_logs[0]
            print(f'  Using existing log {log_name_in_results}')
        else:
            # Clone template log
            template_logs = [log for log in w.logs if log.petrel_name == clone_from]
            if template_logs:
                template = template_logs[0]
                log_obj = template.clone(w, log_name_in_results)
                print(f'  Created new log {log_name_in_results} from template {clone_from}')
            else:
                print(f'  ✗ No template log {clone_from} found in well {w.petrel_name}, skipping')
                continue

        try:
            # Get petrel connection from global scope
            if 'petrel' not in globals():
                print('ERROR: petrel connection not found in global scope')
                return
            
            petrel = globals()['petrel']
            petrel_log_ref = petrel.well_logs[log_obj.path]
            petrel_log_ref.readonly = False
            petrel_log_ref.set_values(md, values)
            print(f'  ✓ Successfully updated {log_name_in_results} for {w.petrel_name}')
            success_count += 1
        except Exception as e:
            print(f'  ✗ Error updating {log_name_in_results} for {w.petrel_name}: {str(e)}')
    
    print(f'\nWrite-back completed: {success_count}/{len(selected_wells)} wells updated successfully.')


def execute_notebook_style_writeback():
    """
    Execute write-back using the notebook-style approach with your current data
    """
    print("="*60)
    print("NOTEBOOK-STYLE WRITE-BACK EXECUTION")
    print("="*60)
    
    # Check required variables
    required_vars = ['petrel', 'selected_wells', 'results']
    missing_vars = []
    
    for var in required_vars:
        if var not in globals():
            missing_vars.append(var)
    
    if missing_vars:
        print(f"ERROR: Missing required variables: {missing_vars}")
        print("Please ensure you have run the ML prediction workflow first.")
        return False
    
    # Get the variables
    results = globals()['results']
    selected_wells = globals()['selected_wells']
    
    print(f"✓ Found results DataFrame: {results.shape}")
    print(f"✓ Found {len(selected_wells)} selected wells")
    
    # Find the target column to write back
    target_columns = [col for col in results.columns if 'VP_COREL' in col and 'imputed' in col]
    
    if not target_columns:
        # Look for any imputed columns
        target_columns = [col for col in results.columns if 'imputed' in col]
    
    if not target_columns:
        print("ERROR: No imputed columns found in results")
        print(f"Available columns: {list(results.columns)}")
        return False
    
    target_column = target_columns[0]
    print(f"✓ Using target column: {target_column}")
    
    # Determine output log name
    if 'VP_COREL_ML_repredicted' in target_column:
        output_log_name = 'VP_COREL_ML_repredicted'
    elif 'VP_COREL' in target_column:
        output_log_name = 'VP_COREL_ML_repredicted'
    else:
        output_log_name = f'{target_column}_output'
    
    print(f"✓ Output log name: {output_log_name}")
    
    # Find template log
    template_log_name = None
    if 'TARGET' in globals():
        template_log_name = globals()['TARGET']
    elif 'selected_target_log' in globals():
        template_log_name = globals()['selected_target_log']
    else:
        # Try to find a VP-related log in the first well
        if selected_wells:
            first_well = selected_wells[0]
            vp_logs = [log.petrel_name for log in first_well.logs if 'VP' in log.petrel_name.upper()]
            if vp_logs:
                template_log_name = vp_logs[0]
            else:
                # Use any available log as template
                if first_well.logs:
                    template_log_name = first_well.logs[0].petrel_name
    
    if not template_log_name:
        print("ERROR: Could not find a suitable template log")
        return False
    
    print(f"✓ Using template log: {template_log_name}")
    
    # Execute the write-back
    print(f"\nExecuting write-back...")
    try:
        write_back_to_petrel(
            results_df=results,
            log_name_in_results=target_column,
            clone_from=template_log_name
        )
        print("✓ Write-back execution completed!")
        return True
    except Exception as e:
        print(f"✗ Error during write-back: {str(e)}")
        import traceback
        print(f"Debug traceback: {traceback.format_exc()}")
        return False


# Make functions available globally
globals()['write_back_to_petrel'] = write_back_to_petrel
globals()['execute_notebook_style_writeback'] = execute_notebook_style_writeback

print("✓ Notebook-style write-back functions loaded!")
print("\nTo execute write-back using the notebook pattern:")
print("  execute_notebook_style_writeback()")
print("\nOr call directly:")
print("  write_back_to_petrel(results, 'VP_COREL_ML_imputed', 'VP_COREL_ML')")
print("\nThis implementation follows the exact pattern from:")
print("  missing_logs_imputation_petrel_enhanced.ipynb")
